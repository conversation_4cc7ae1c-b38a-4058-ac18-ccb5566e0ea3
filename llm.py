from __future__ import annotations

import os
import json
from typing import Optional

import httpx

OLLAMA_URL = os.getenv("OLLAMA_URL", "http://localhost:11434").rstrip("/")
OLLAMA_MODEL = os.getenv("OLLAMA_MODEL", "deepseek-r1:14b")


def _parse(text: str) -> str:
    # Prefer single JSON with "response"
    try:
        obj = json.loads(text)
        if isinstance(obj, dict) and isinstance(obj.get("response"), str):
            return obj["response"].strip()
    except json.JSONDecodeError:
        pass
    # Tolerate NDJSON streams (join "response" chunks)
    parts = []
    for line in text.splitlines():
        line = line.strip()
        if not line:
            continue
        try:
            chunk = json.loads(line)
            if isinstance(chunk, dict) and isinstance(chunk.get("response"), str):
                parts.append(chunk["response"])
        except json.JSONDecodeError:
            continue
    return ("".join(parts) if parts else text).strip()


async def generate_from_ollama(
    prompt: str,
    model: Optional[str] = None,
    max_tokens: int = 512,
    temperature: float = 0.2,
    timeout: int = 60,
) -> str:
    """Send a prompt to Ollama /api/generate and return the model text."""
    url = f"{OLLAMA_URL}/api/generate"
    payload = {
        "model": model or OLLAMA_MODEL,
        "prompt": prompt,
        "stream": False,  # single JSON object back per docs
        "options": {"num_predict": max_tokens, "temperature": temperature},
    }
    async with httpx.AsyncClient(timeout=timeout) as client:
        resp = await client.post(url, json=payload)
        resp.raise_for_status()
        return _parse(resp.text)


def generate_from_ollama_sync(
    *, prompt: str,
    model: Optional[str] = None,
    max_tokens: int = 512,
    temperature: float = 0.2,
    timeout: int = 60,
) -> str:
    """Sync wrapper: call this WITHOUT `await`."""
    url = f"{OLLAMA_URL}/api/generate"
    payload = {
        "model": model or OLLAMA_MODEL,
        "prompt": prompt,
        "stream": False,
        "options": {"num_predict": max_tokens, "temperature": temperature},
    }
    with httpx.Client(timeout=timeout) as client:
        resp = client.post(url, json=payload)
        resp.raise_for_status()
        return _parse(resp.text)
