import os, json, sqlite3
from fastapi import FastAP<PERSON>, Request, Header, HTTPException
import httpx
from dotenv import load_dotenv
from llm import generate_from_ollama_sync


load_dotenv()
BOT_TOKEN = os.environ["TELEGRAM_BOT_TOKEN"]
SECRET = os.environ["SECRET_TOKEN"]
MAX = 4096
TICKERS_ENV = os.getenv("WATCH_TICKERS")

app = FastAPI()

# banco mínimo
def init_db():
		conn = sqlite3.connect("bot.db")
		conn.execute("""
		CREATE TABLE IF NOT EXISTS messages (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				update_id INTEGER,
				chat_id INTEGER,
				username TEXT,
				text TEXT,
				payload TEXT,
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)""")
		conn.commit()
		conn.close()

init_db()

async def handle_message(text, from_user):
	if text in ("/start", "oi", "olá", "ola", 'opa'):
		return 'Oi, ' + from_user.get("first_name") + '!\n\n' + 'Eu sou a Miriê da Toque da Terra, vou te ajudar!'
	else:
		return generate_from_ollama_sync(prompt=text)

@app.get("/")
def health():
		return {"ok": True}

@app.post("/telegram/webhook")
async def telegram_webhook(
		request: Request,
		x_telegram_bot_api_secret_token: str = Header(None)
):
		if x_telegram_bot_api_secret_token != SECRET:
				raise HTTPException(status_code=401, detail="Invalid secret token")

		update = await request.json()
		message = update.get("message") or {}
		chat = message.get("chat") or {}
		from_user = message.get("from") or {}

		chat_id = chat.get("id")
		text = message.get("text")

		# salva cru
		conn = sqlite3.connect("bot.db")
		conn.execute(
				"INSERT INTO messages (update_id, chat_id, username, text, payload) VALUES (?, ?, ?, ?, ?)",
				(update.get("update_id"), chat_id, from_user.get("username"), text, json.dumps(update)),
		)
		conn.commit()
		conn.close()
		
		if chat_id:
   
			async with httpx.AsyncClient() as client:
				try:
					print("text: ", message.get("text"), "from_user: ", from_user.get("first_name"))
					reply = await handle_message(text, from_user)
     
					r = await client.post(
							f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage",
							json={"chat_id": chat_id, "text": reply, "disable_web_page_preview": True}
					)
     
					data = r.json()
     
					if not data.get("ok"):
							print("Telegram API error:", data)
							raise RuntimeError(f"sendMessage failed: {data}")
     
				except Exception as e:
					print("error sending reply: ", e)
     
				finally:
					pass
  
